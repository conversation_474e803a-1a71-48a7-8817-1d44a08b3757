import React, { useState, useEffect, useRef } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowLeft,
  MapPin,
  Phone,
  Car,
  Navigation,
  Clock,
  User,
  Package,
  Maximize2,
  Minimize2,
  Play,
  Pause,
  CheckCircle2,
  Timer,
  Truck,
  Sparkles,
  Zap,
  Award,
  Target
} from 'lucide-react';
import { <PERSON><PERSON><PERSON>r, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { useOrdersStore } from '../../../stores/ordersStore';

// Fix for default markers in Leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Custom driver marker icon
const driverIcon = new L.Icon({
  iconUrl: 'data:image/svg+xml;base64,' + btoa(`
    <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="20" cy="20" r="18" fill="#f97316" stroke="white" stroke-width="4"/>
      <path d="M12 20h16M20 12v16" stroke="white" stroke-width="3" stroke-linecap="round"/>
      <circle cx="20" cy="20" r="3" fill="white"/>
    </svg>
  `),
  iconSize: [40, 40],
  iconAnchor: [20, 20],
  popupAnchor: [0, -20],
});

// Custom destination marker icon
const destinationIcon = new L.Icon({
  iconUrl: 'data:image/svg+xml;base64=' + btoa(`
    <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="20" cy="20" r="18" fill="#10b981" stroke="white" stroke-width="4"/>
      <path d="M20 8v24M8 20h24" stroke="white" stroke-width="3" stroke-linecap="round"/>
      <circle cx="20" cy="20" r="3" fill="white"/>
    </svg>
  `),
  iconSize: [40, 40],
  iconAnchor: [20, 20],
  popupAnchor: [0, -20],
});

// Enhanced Glass Card Component with Extreme Effects
const GlassCard: React.FC<{ children: React.ReactNode; className?: string; gradient?: string }> = ({ 
  children, 
  className = '', 
  gradient = 'from-white/10 to-white/5' 
}) => (
  <motion.div
    className={`backdrop-blur-xl bg-gradient-to-br ${gradient} border border-white/20 rounded-3xl shadow-2xl ${className}`}
    style={{
      boxShadow: '0 32px 64px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2)',
      position: 'relative',
    }}
  >
    {/* Enhanced Shimmer effect */}
    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full animate-[shimmer_3s_infinite] pointer-events-none" />
    
    {/* Subtle inner glow */}
    <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-white/5 to-transparent pointer-events-none" />
    
    {/* Content */}
    <div className="relative z-10">
      {children}
    </div>
  </motion.div>
);

// Simple Background Orb Component - NO BLUR, LOW Z-INDEX
const FloatingOrb: React.FC<{
  size: number;
  color: string;
  delay: number;
  duration: number;
  x: string;
  y: string;
}> = ({ size, color, delay, duration, x, y }) => (
  <motion.div
    className={`absolute rounded-full ${color}`}
    style={{
      width: size,
      height: size,
      left: x,
      top: y,
      opacity: 0.06, // Very low opacity to prevent blur interference
      zIndex: -1, // Behind everything
      pointerEvents: 'none',
    }}
    animate={{
      x: [0, 30, -20, 0],
      y: [0, -20, 30, 0],
      scale: [1, 1.2, 0.8, 1],
    }}
    transition={{
      duration,
      delay,
      repeat: Infinity,
      ease: "easeInOut",
    }}
  />
);

// Simple Particle System Component - Behind everything
const ParticleSystem: React.FC = () => (
  <div className="absolute inset-0 overflow-hidden pointer-events-none" style={{ zIndex: -2 }}>
    {Array.from({ length: 10 }).map((_, i) => (
      <motion.div
        key={i}
        className="absolute w-1 h-1 bg-white rounded-full opacity-10"
        style={{
          left: `${Math.random() * 100}%`,
          top: `${Math.random() * 100}%`,
          pointerEvents: 'none',
        }}
        animate={{
          y: [0, -100, 0],
          opacity: [0, 0.1, 0],
          scale: [0, 1, 0],
        }}
        transition={{
          duration: 5 + Math.random() * 3,
          delay: Math.random() * 8,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
    ))}
  </div>
);

const SupplierTracking: React.FC = () => {
  const { orderId } = useParams<{ orderId: string }>();
  const navigate = useNavigate();
  const { orders } = useOrdersStore();
  const order = orders.find((o) => o.id === orderId);

  const [simulatedDriver, setSimulatedDriver] = useState<{lat: number; lng: number; address?: string}>(() => {
    console.log('Initializing simulatedDriver with order:', order);
    // Initialize with driver location if order exists and has driver location
    if (order?.driverLocation) {
      console.log('Using order driverLocation:', order.driverLocation);
      return order.driverLocation;
    }
    // Fallback to a default location near the delivery address
    const fallbackLocation = order?.deliveryLocation ? {
      lat: order.deliveryLocation.lat - 0.001,
      lng: order.deliveryLocation.lng - 0.001,
      address: 'Driver location'
    } : {
      lat: 32.2200,
      lng: 35.2530,
      address: 'Driver location'
    };
    console.log('Using fallback location:', fallbackLocation);
    return fallbackLocation;
  });
  const [mapFullscreen, setMapFullscreen] = useState(false);
  const [estimatedTime, setEstimatedTime] = useState(12);
  const [isLiveTracking, setIsLiveTracking] = useState(true);

  const dropoff = order?.deliveryLocation?.lng && order?.deliveryLocation?.lat
    ? [order.deliveryLocation.lng, order.deliveryLocation.lat]
    : [35.2544, 32.2211]; // Nablus, Palestine

  const driverCoord: [number, number] = [simulatedDriver.lng, simulatedDriver.lat];

  // Enhanced driver simulation with realistic movement
  useEffect(() => {
    if (!isLiveTracking) return;

    const interval = setInterval(() => {
      setSimulatedDriver((prev) => {
        if (!prev) return prev;

        // Calculate distance to destination
        const distance = Math.sqrt(
          Math.pow(dropoff[0] - prev.lng, 2) + Math.pow(dropoff[1] - prev.lat, 2)
        );

        // If very close to destination, stop moving
        if (distance < 0.0001) {
          setIsLiveTracking(false);
          setEstimatedTime(0);
          return prev;
        }

        // Move towards destination with some randomness
        const moveSpeed = 0.00008;
        const randomFactor = 0.00002;

        return {
          lng: prev.lng + (Math.random() - 0.5) * randomFactor + moveSpeed,
          lat: prev.lat + (Math.random() - 0.5) * randomFactor + moveSpeed * 0.6,
          address: 'Moving towards destination...'
        };
      });

      // Update estimated time
      setEstimatedTime(prev => Math.max(0, prev - 0.5));
    }, 3000);

    return () => clearInterval(interval);
  }, [simulatedDriver, isLiveTracking, dropoff]);

  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'Preparing':
        return {
          color: '#f59e0b',
          bgColor: ['#f59e0b', '#d97706'],
          icon: Timer,
          label: 'Preparing Order'
        };
      case 'On the Way':
        return {
          color: '#f97316',
          bgColor: ['#f97316', '#ea580c'],
          icon: Car,
          label: 'On the Way'
        };
      case 'Delivered':
        return {
          color: '#10b981',
          bgColor: ['#10b981', '#059669'],
          icon: CheckCircle2,
          label: 'Delivered'
        };
      default:
        return {
          color: '#6b7280',
          bgColor: ['#6b7280', '#4b5563'],
          icon: Package,
          label: status
        };
    }
  };

  const statusConfig = getStatusConfig(order?.status ?? '');

  if (!order) {
    return (
      <div className="min-h-screen relative overflow-hidden">
        {/* Background - Same as supplier home */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-indigo-900">
          <FloatingOrb size={450} color="bg-purple-500" delay={0} duration={25} x="5%" y="15%" />
          <FloatingOrb size={380} color="bg-blue-500" delay={2} duration={30} x="75%" y="25%" />
          <FloatingOrb size={320} color="bg-pink-500" delay={4} duration={22} x="15%" y="65%" />
          <FloatingOrb size={300} color="bg-indigo-500" delay={6} duration={28} x="85%" y="75%" />
          <FloatingOrb size={280} color="bg-cyan-500" delay={8} duration={35} x="45%" y="45%" />
          <FloatingOrb size={200} color="bg-emerald-500" delay={10} duration={20} x="60%" y="10%" />
          <ParticleSystem />
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10 pointer-events-none" />
        </div>

        <div className="relative min-h-screen flex items-center justify-center p-8" style={{ zIndex: 1 }}>
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ type: 'spring', damping: 15 }}
          >
            <GlassCard className="p-12 text-center max-w-md">
              <div className="bg-white/10 rounded-2xl p-6 w-20 h-20 mx-auto mb-6 flex items-center justify-center">
                <Package size={40} className="text-gray-400" />
              </div>
              <h3 className="text-white text-2xl font-bold mb-4">Order Not Found</h3>
              <p className="text-white/80 mb-8">
                Unable to track this order. It may have been completed or cancelled.
              </p>
              <button
                onClick={() => navigate(-1)}
                className="bg-gradient-to-r from-purple-600 to-purple-700 text-white px-8 py-3 rounded-xl font-semibold hover:from-purple-700 hover:to-purple-800 transition-all duration-300 flex items-center gap-3 mx-auto"
              >
                <ArrowLeft size={20} />
                Go Back
              </button>
            </GlassCard>
          </motion.div>
        </div>
      </div>
    );
  }



  return (
    <>
      {/* Modern CSS Animations */}
      <style>{`
        @keyframes shimmer {
          0% { transform: translateX(-100%) translateY(-10px) rotate(-5deg); }
          100% { transform: translateX(100%) translateY(10px) rotate(5deg); }
        }
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-15px) rotate(2deg); }
        }
        @keyframes glow {
          0%, 100% { box-shadow: 0 0 30px rgba(139, 92, 246, 0.4); }
          50% { box-shadow: 0 0 60px rgba(139, 92, 246, 0.8); }
        }
        @keyframes pulse {
          0%, 100% { opacity: 0.8; transform: scale(1); }
          50% { opacity: 1; transform: scale(1.08); }
        }
        @keyframes drift {
          0%, 100% { transform: translate(0px, 0px) rotate(0deg); }
          25% { transform: translate(30px, -25px) rotate(2deg); }
          50% { transform: translate(-20px, 20px) rotate(-2deg); }
          75% { transform: translate(25px, 15px) rotate(1deg); }
        }
        @keyframes sparkle {
          0%, 100% { opacity: 0; transform: scale(0) rotate(0deg); }
          50% { opacity: 1; transform: scale(1.2) rotate(180deg); }
        }
        @keyframes gradient-shift {
          0%, 100% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
        }
        @keyframes wave {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
        @keyframes breathe {
          0%, 100% { transform: scale(1) rotate(0deg); }
          50% { transform: scale(1.05) rotate(1deg); }
        }
        @keyframes twinkle {
          0%, 100% { opacity: 0.3; }
          50% { opacity: 1; }
        }
      `}</style>

      <div className="min-h-screen relative overflow-hidden">
        {/* Background - Behind everything */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-indigo-900">
          {/* Floating Orbs - Behind everything */}
          <FloatingOrb size={450} color="bg-purple-500" delay={0} duration={25} x="5%" y="15%" />
          <FloatingOrb size={380} color="bg-blue-500" delay={2} duration={30} x="75%" y="25%" />
          <FloatingOrb size={320} color="bg-pink-500" delay={4} duration={22} x="15%" y="65%" />
          <FloatingOrb size={300} color="bg-indigo-500" delay={6} duration={28} x="85%" y="75%" />
          <FloatingOrb size={280} color="bg-cyan-500" delay={8} duration={35} x="45%" y="45%" />
          <FloatingOrb size={200} color="bg-emerald-500" delay={10} duration={20} x="60%" y="10%" />

          {/* Particle System */}
          <ParticleSystem />

          {/* Animated gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10 pointer-events-none" />
        </div>

        {/* Enhanced Fullscreen Map Modal */}
        <AnimatePresence>
          {mapFullscreen && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-50 bg-black"
            >
              <div className="relative w-full h-full">
                <MapContainer
                  center={[dropoff[1], dropoff[0]]}
                  zoom={15}
                  style={{ height: '100%', width: '100%' }}
                  zoomControl={true}
                >
                  <TileLayer
                    attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                    url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                  />

                  {/* Driver Marker */}
                  <Marker position={[driverCoord[1], driverCoord[0]]} icon={driverIcon}>
                    <Popup>
                      <div className="text-center">
                        <strong>Driver: {order.driverName}</strong><br />
                        ETA: {estimatedTime} mins
                      </div>
                    </Popup>
                  </Marker>

                  {/* Destination Marker */}
                  <Marker position={[dropoff[1], dropoff[0]]} icon={destinationIcon}>
                    <Popup>
                      <div className="text-center">
                        <strong>Delivery Destination</strong><br />
                        {order.address}
                      </div>
                    </Popup>
                  </Marker>

                  {/* Route Line */}
                  <Polyline
                    positions={[
                      [driverCoord[1], driverCoord[0]],
                      [dropoff[1], dropoff[0]]
                    ]}
                    color="#7c3aed"
                    weight={4}
                    dashArray="10, 5"
                  />
                </MapContainer>

                {/* Enhanced Map Controls */}
                <div className="absolute top-6 left-6 right-6 z-[1000]">
                  <GlassCard
                    gradient="from-white/95 to-white/90"
                    className="p-4 border-white/30"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div
                          className={`w-3 h-3 rounded-full ${isLiveTracking ? 'bg-green-500' : 'bg-gray-500'}`}
                        />
                        <span className="text-gray-800 text-sm font-semibold">
                          {isLiveTracking ? 'Live Tracking' : 'Tracking Paused'}
                        </span>
                      </div>

                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setMapFullscreen(false)}
                        className="bg-purple-600 hover:bg-purple-700 text-white p-2 rounded-full transition-colors duration-200"
                      >
                        <Minimize2 size={20} />
                      </motion.button>
                    </div>
                  </GlassCard>
                </div>

                {/* Driver Info Overlay */}
                <div className="absolute bottom-6 left-6 right-6 z-[1000]">
                  <GlassCard
                    gradient="from-white/95 to-white/90"
                    className="p-6 border-white/30"
                  >
                    <div className="flex items-center gap-4">
                      <div className={`bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl p-3`}>
                        <Car size={24} className="text-white" />
                      </div>

                      <div className="flex-1">
                        <h4 className="text-gray-800 text-lg font-bold">
                          {order.driverName}
                        </h4>
                        <p className="text-gray-600 text-sm">
                          ETA: {estimatedTime > 0 ? `${estimatedTime} minutes` : 'Arriving now!'}
                        </p>
                      </div>

                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => window.open(`tel:${order.driverPhone}`)}
                        className="bg-green-600 hover:bg-green-700 text-white p-3 rounded-full transition-colors duration-200"
                      >
                        <Phone size={18} />
                      </motion.button>
                    </div>
                  </GlassCard>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main Content Container - Normal z-index to not interfere with header */}
        <div className="relative min-h-screen w-full p-8 pb-24" style={{ zIndex: 1 }}>
          <div className="max-w-4xl mx-auto space-y-8">
            {/* Back Button */}
            <motion.button
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              onClick={() => navigate(-1)}
              className="flex items-center gap-3 text-white/80 hover:text-white transition-colors duration-300 mb-6"
            >
              <ArrowLeft size={24} />
              <span className="text-lg font-medium">Back to Orders</span>
            </motion.button>

            {/* EXTREME Tracking Header */}
            <motion.div
              initial={{ opacity: 0, y: -50, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ type: 'spring', damping: 20, stiffness: 300 }}
            >
              <GlassCard
                className="overflow-hidden border-0 shadow-2xl"
                gradient={`from-${statusConfig.color.replace('#', '')}/25 to-${statusConfig.color.replace('#', '')}/15`}
              >
                <div className={`bg-gradient-to-br from-orange-500 to-orange-600 p-12 relative`}>
                  {/* Decorative Background Elements */}
                  <div className="absolute top-0 right-0 w-40 h-40 bg-white/10 rounded-full -translate-y-20 translate-x-20" />
                  <div className="absolute bottom-0 left-0 w-32 h-32 bg-white/8 rounded-full translate-y-16 -translate-x-16" />

                  <div className="relative z-10 space-y-6">
                    {/* Order Header */}
                    <div className="flex items-center gap-6">
                      <motion.div
                        initial={{ scale: 0, rotate: -180 }}
                        animate={{ scale: 1, rotate: 0 }}
                        transition={{ delay: 0.4, type: 'spring', damping: 15 }}
                        className="relative"
                      >
                        <div className="relative">
                          <div className="bg-white/25 backdrop-blur-sm border-2 border-white/40 rounded-3xl p-6">
                            <MapPin size={48} className="text-white" />
                          </div>

                          {/* Live Tracking Indicator */}
                          {isLiveTracking && (
                            <motion.div
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{ delay: 0.6, type: 'spring', damping: 10 }}
                              className="absolute -top-2 -right-2"
                            >
                              <div className="bg-green-500 rounded-full p-2 border-2 border-white">
                                <div className="w-2 h-2 rounded-full bg-white" />
                              </div>
                            </motion.div>
                          )}
                        </div>
                      </motion.div>

                      <div className="flex-1 space-y-3">
                        <motion.h1
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.6, duration: 0.6 }}
                          className="text-white text-5xl font-black"
                        >
                          🚚 Live Tracking
                        </motion.h1>

                        <motion.div
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.7, duration: 0.6 }}
                          className="flex items-center gap-4"
                        >
                          <div className="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2">
                            <span className="text-white text-sm font-bold">
                              ORDER #{order.id}
                            </span>
                          </div>
                          <span className="text-white/90 text-lg font-medium">
                            {statusConfig.label}
                          </span>
                        </motion.div>
                      </div>
                    </div>

                    {/* ETA and Driver Info */}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.8, duration: 0.6 }}
                      className="flex items-center justify-between"
                    >
                      <div className="text-center space-y-2 flex-1">
                        <p className="text-white/80 text-sm font-semibold">ETA</p>
                        <p className="text-white text-3xl font-black">
                          {estimatedTime > 0 ? `${estimatedTime}m` : 'NOW!'}
                        </p>
                      </div>

                      <div className="w-px h-16 bg-white/30 mx-6"></div>

                      <div className="text-center space-y-2 flex-1">
                        <p className="text-white/80 text-sm font-semibold">DRIVER</p>
                        <p className="text-white text-xl font-bold">
                          {order.driverName}
                        </p>
                      </div>

                      <div className="w-px h-16 bg-white/30 mx-6"></div>

                      <div className="text-center space-y-2 flex-1">
                        <p className="text-white/80 text-sm font-semibold">STATUS</p>
                        <p className="text-white text-xl font-bold">
                          {isLiveTracking ? 'LIVE' : 'PAUSED'}
                        </p>
                      </div>
                    </motion.div>
                  </div>
                </div>
              </GlassCard>
            </motion.div>

            {/* Enhanced Map Preview */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, duration: 0.5 }}
            >
              <GlassCard
                className="overflow-hidden border-0 shadow-2xl"
                gradient="from-white/15 to-white/10"
              >
                <div className="bg-gradient-to-br from-purple-500 to-purple-600 p-8 relative overflow-hidden">
                  <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16" />
                  <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/8 rounded-full translate-y-12 -translate-x-12" />

                  <div className="relative z-10 flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="bg-white/20 backdrop-blur-sm rounded-xl p-3">
                        <Navigation size={24} className="text-white" />
                      </div>
                      <div>
                        <h3 className="text-white text-2xl font-bold">Live Map View</h3>
                        <p className="text-white/90 text-sm">Tap to open full tracking</p>
                      </div>
                    </div>

                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => setMapFullscreen(true)}
                      className="bg-white/20 backdrop-blur-sm border-2 border-white/30 rounded-full p-3 hover:bg-white/30 transition-all duration-300"
                    >
                      <Maximize2 size={18} className="text-white" />
                    </motion.button>
                  </div>
                </div>

                <motion.div
                  whileHover={{ scale: 1.02 }}
                  onClick={() => setMapFullscreen(true)}
                  className="bg-white p-8 h-48 flex items-center justify-center cursor-pointer"
                >
                  <div className="text-center space-y-4">
                    <div className="flex items-center justify-center gap-8">
                      <div className="text-center space-y-2">
                        <div className="bg-orange-500 rounded-2xl p-4 border-2 border-white shadow-lg mx-auto w-fit">
                          <Car size={28} className="text-white" />
                        </div>
                        <p className="text-sm font-semibold text-gray-700">Driver</p>
                        <p className="text-xs text-gray-500">{order.driverName}</p>
                      </div>

                      <div className="text-center">
                        <div className="w-20 h-1 bg-purple-500 rounded-full mb-2" />
                        <p className="text-xs text-gray-500 font-semibold">
                          {estimatedTime > 0 ? `${estimatedTime} mins` : 'Arriving!'}
                        </p>
                      </div>

                      <div className="text-center space-y-2">
                        <div className="bg-green-500 rounded-2xl p-4 border-2 border-white shadow-lg mx-auto w-fit">
                          <MapPin size={28} className="text-white" />
                        </div>
                        <p className="text-sm font-semibold text-gray-700">Destination</p>
                        <p className="text-xs text-gray-500">Customer</p>
                      </div>
                    </div>

                    <div className="bg-gray-100 rounded-lg px-4 py-2">
                      <p className="text-sm text-gray-700 font-semibold">
                        📍 Tap to view full map with live tracking
                      </p>
                    </div>
                  </div>
                </motion.div>
              </GlassCard>
            </motion.div>

            {/* Enhanced Destination Info */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3, duration: 0.5 }}
            >
              <GlassCard
                className="p-8 bg-white/10 border-white/20"
                gradient="from-white/15 to-white/10"
              >
                <div className="space-y-6">
                  <div className="flex items-center gap-4">
                    <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl p-3">
                      <MapPin size={24} className="text-white" />
                    </div>
                    <h3 className="text-white text-2xl font-bold">Delivery Details</h3>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-start gap-4 p-4 bg-white/5 rounded-xl border border-white/10 hover:bg-white/8 transition-all duration-300">
                      <div className="bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-lg p-3 mt-1">
                        <MapPin size={20} className="text-white" />
                      </div>
                      <div className="flex-1 space-y-1">
                        <p className="text-emerald-400 text-sm font-bold uppercase tracking-wide">ADDRESS</p>
                        <p className="text-white text-lg font-medium leading-relaxed">
                          {order.address || 'No address provided'}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-4 p-4 bg-white/5 rounded-xl border border-white/10 hover:bg-white/8 transition-all duration-300">
                      <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg p-3">
                        <Phone size={20} className="text-white" />
                      </div>
                      <div className="flex-1 space-y-1">
                        <p className="text-blue-400 text-sm font-bold uppercase tracking-wide">PHONE</p>
                        <p className="text-white text-lg font-medium">
                          {order.phone}
                        </p>
                      </div>
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => window.open(`tel:${order.phone}`)}
                        className="bg-green-600 hover:bg-green-700 text-white p-3 rounded-full transition-colors duration-200"
                      >
                        <Phone size={18} />
                      </motion.button>
                    </div>

                    <div className="flex items-center gap-4 p-4 bg-white/5 rounded-xl border border-white/10 hover:bg-white/8 transition-all duration-300">
                      <div className="bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg p-3">
                        <Car size={20} className="text-white" />
                      </div>
                      <div className="flex-1 space-y-1">
                        <p className="text-purple-400 text-sm font-bold uppercase tracking-wide">DRIVER</p>
                        <p className="text-white text-lg font-medium">
                          {order.driverName} • {order.driverPhone}
                        </p>
                      </div>
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => window.open(`tel:${order.driverPhone}`)}
                        className="bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full transition-colors duration-200"
                      >
                        <Phone size={18} />
                      </motion.button>
                    </div>

                    {order.notes && (
                      <div className="flex items-start gap-4 p-4 bg-white/5 rounded-xl border border-white/10 hover:bg-white/8 transition-all duration-300">
                        <div className="bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-lg p-3 mt-1">
                          <Package size={20} className="text-white" />
                        </div>
                        <div className="flex-1 space-y-1">
                          <p className="text-yellow-400 text-sm font-bold uppercase tracking-wide">SPECIAL NOTE</p>
                          <p className="text-white text-lg font-medium leading-relaxed">
                            {order.notes}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </GlassCard>
            </motion.div>

            {/* Enhanced Timeline */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.4, duration: 0.5 }}
            >
              <GlassCard
                className="p-8 bg-white/10 border-white/20"
                gradient="from-white/15 to-white/10"
              >
                <div className="space-y-6">
                  <div className="flex items-center gap-4">
                    <div className="bg-gradient-to-br from-green-500 to-green-600 rounded-xl p-3">
                      <Clock size={24} className="text-white" />
                    </div>
                    <h3 className="text-white text-2xl font-bold">Delivery Progress</h3>
                  </div>

                  <div className="flex items-center justify-between px-4">
                    {[
                      { stage: 'Preparing', icon: Timer, color: '#f59e0b' },
                      { stage: 'On the Way', icon: Car, color: '#f97316' },
                      { stage: 'Delivered', icon: CheckCircle2, color: '#10b981' }
                    ].map((item, index) => {
                      const isActive = order.status === item.stage;
                      const isCompleted =
                        (order.status === 'On the Way' && item.stage === 'Preparing') ||
                        (order.status === 'Delivered' && item.stage !== 'Delivered');
                      const isActiveOrCompleted = isActive || isCompleted;
                      const IconComponent = item.icon;

                      return (
                        <div key={item.stage} className="text-center flex-1">
                          <motion.div
                            initial={{ scale: 0.8, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            transition={{ delay: 0.5 + index * 0.2, type: 'spring', damping: 15 }}
                            className="relative"
                          >
                            <div
                              className={`rounded-2xl p-4 border-2 transition-all duration-300 ${
                                isActiveOrCompleted
                                  ? 'border-white shadow-lg'
                                  : 'border-transparent'
                              }`}
                              style={{
                                backgroundColor: isActiveOrCompleted ? item.color : '#e5e7eb',
                                boxShadow: isActiveOrCompleted ? `0 4px 8px ${item.color}30` : 'none'
                              }}
                            >
                              <IconComponent
                                size={24}
                                className={isActiveOrCompleted ? 'text-white' : 'text-gray-400'}
                              />
                            </div>
                          </motion.div>

                          <p
                            className={`text-sm mt-3 font-semibold transition-colors duration-300 ${
                              isActiveOrCompleted ? 'text-white' : 'text-gray-400'
                            }`}
                          >
                            {item.stage}
                          </p>

                          {isActive && (
                            <motion.div
                              initial={{ opacity: 0, scale: 0.8 }}
                              animate={{ opacity: 1, scale: 1 }}
                              transition={{ delay: 0.8, duration: 0.4 }}
                              className="mt-2"
                            >
                              <div
                                className="rounded px-2 py-1"
                                style={{ backgroundColor: item.color }}
                              >
                                <span className="text-white text-xs font-bold">ACTIVE</span>
                              </div>
                            </motion.div>
                          )}
                        </div>
                      );
                    })}
                  </div>

                  {/* Progress Bar */}
                  <div className="bg-gray-300 rounded-full h-1 overflow-hidden mt-6">
                    <motion.div
                      initial={{ width: '0%' }}
                      animate={{
                        width: order.status === 'Preparing' ? '33%' :
                               order.status === 'On the Way' ? '66%' :
                               order.status === 'Delivered' ? '100%' : '0%'
                      }}
                      transition={{ delay: 1, duration: 1 }}
                      className="h-full bg-green-500 rounded-full"
                    />
                  </div>
                </div>
              </GlassCard>
            </motion.div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SupplierTracking;
